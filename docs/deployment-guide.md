# 股票分析RAG系统 - 部署指南

## 📋 概述

本文档提供股票分析RAG系统的完整部署指南，包括开发环境、测试环境和生产环境的部署方法。

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Nginx       │    │   Streamlit     │    │   FastAPI       │
│   (反向代理)     │────│    (前端)       │────│    (后端)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │     Redis       │    │     MySQL       │
         │              │    (缓存)       │    │   (数据库)      │
         │              └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐    ┌─────────────────┐
         └──────────────│    ChromaDB     │    │     Ollama      │
                        │  (向量数据库)    │    │   (LLM服务)     │
                        └─────────────────┘    └─────────────────┘
```

## 🚀 快速部署

### 使用Docker Compose (推荐)

```bash
# 1. 克隆项目
git clone <repository-url>
cd llm-rag

# 2. 运行部署脚本
./deploy/deploy.sh

# 3. 访问系统
# 前端界面: http://localhost
# API文档: http://localhost/api/docs
```

### 手动部署

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 3. 启动服务
python start_server.py &
python start_streamlit.py &
```

## 📦 环境要求

### 系统要求

- **操作系统**: Linux (Ubuntu 20.04+), macOS (10.15+), Windows 10+
- **内存**: 最少 8GB，推荐 16GB+
- **存储**: 最少 20GB 可用空间
- **网络**: 稳定的互联网连接

### 软件依赖

- **Python**: 3.11+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **MySQL**: 8.0+
- **Redis**: 6.0+

## 🔧 配置说明

### 环境变量配置

创建 `.env` 文件并配置以下变量：

```bash
# 基本配置
ENVIRONMENT=production
DEBUG=false
API_HOST=0.0.0.0
API_PORT=8000

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=stock_cursor

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:7b

# ChromaDB配置
CHROMA_PERSIST_DIRECTORY=./data/chroma
CHROMA_COLLECTION_NAME=stock_documents

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
```

### 数据库配置

#### MySQL配置

```sql
-- 创建数据库
CREATE DATABASE stock_cursor CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'stock_user'@'%' IDENTIFIED BY 'stock_password';
GRANT ALL PRIVILEGES ON stock_cursor.* TO 'stock_user'@'%';
FLUSH PRIVILEGES;
```

#### Redis配置

```bash
# redis.conf
bind 0.0.0.0
port 6379
maxmemory 2gb
maxmemory-policy allkeys-lru
```

## 🐳 Docker部署

### 构建镜像

```bash
# 构建应用镜像
docker build -t stock-rag-system .

# 或使用docker-compose构建
docker-compose build
```

### 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 服务配置

#### docker-compose.yml 主要配置

```yaml
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - ollama

  frontend:
    build: .
    ports:
      - "8501:8501"
    command: ["python", "start_streamlit.py"]
    depends_on:
      - app

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./deploy/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
      - frontend
```

## 🔒 安全配置

### SSL/TLS配置

```bash
# 生成自签名证书 (开发环境)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout deploy/ssl/key.pem \
  -out deploy/ssl/cert.pem

# 配置Nginx HTTPS
# 编辑 deploy/nginx.conf 启用HTTPS部分
```

### 防火墙配置

```bash
# Ubuntu/Debian
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### 数据库安全

```sql
-- 删除默认用户
DROP USER IF EXISTS ''@'localhost';
DROP USER IF EXISTS ''@'%';

-- 设置强密码策略
SET GLOBAL validate_password.policy = STRONG;
SET GLOBAL validate_password.length = 12;
```

## 📊 监控和日志

### 日志配置

```python
# logging.conf
[loggers]
keys=root,app

[handlers]
keys=consoleHandler,fileHandler

[formatters]
keys=simpleFormatter

[logger_root]
level=INFO
handlers=consoleHandler

[logger_app]
level=INFO
handlers=consoleHandler,fileHandler
qualname=app
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=simpleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=FileHandler
level=INFO
formatter=simpleFormatter
args=('logs/app.log',)

[formatter_simpleFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s
```

### 健康检查

```bash
# API健康检查
curl http://localhost/health

# 系统状态检查
curl http://localhost/api/v1/system/status

# 数据库连接检查
docker-compose exec mysql mysqladmin ping
```

### 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop
iostat -x 1
```

## 🔧 故障排除

### 常见问题

#### 1. 端口占用

```bash
# 检查端口占用
lsof -i :8000
lsof -i :8501

# 杀死占用进程
kill -9 <PID>
```

#### 2. 数据库连接失败

```bash
# 检查MySQL服务
docker-compose exec mysql mysqladmin ping

# 检查连接配置
docker-compose exec app python -c "
from database.mysql_client import mysql_client
print(mysql_client.test_connection())
"
```

#### 3. 内存不足

```bash
# 检查内存使用
free -h
docker stats

# 清理Docker资源
docker system prune -f
docker volume prune -f
```

#### 4. Ollama模型加载失败

```bash
# 检查Ollama服务
curl http://localhost:11434/api/tags

# 拉取模型
docker-compose exec ollama ollama pull qwen2.5:7b
```

### 日志分析

```bash
# 查看应用日志
docker-compose logs app

# 查看前端日志
docker-compose logs frontend

# 查看数据库日志
docker-compose logs mysql

# 实时监控日志
docker-compose logs -f --tail=100
```

## 📈 性能优化

### 数据库优化

```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = 2G;
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 256M;
```

### 应用优化

```python
# 连接池配置
MYSQL_POOL_SIZE = 20
MYSQL_MAX_OVERFLOW = 30
MYSQL_POOL_TIMEOUT = 30

# Redis缓存配置
REDIS_MAX_CONNECTIONS = 50
CACHE_TTL = 3600
```

### Nginx优化

```nginx
# worker进程数
worker_processes auto;

# 连接数限制
worker_connections 1024;

# 缓存配置
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m;
proxy_cache my_cache;
proxy_cache_valid 200 1h;
```

## 🔄 备份和恢复

### 数据备份

```bash
# MySQL备份
docker-compose exec mysql mysqldump -u root -p stock_cursor > backup.sql

# ChromaDB备份
tar -czf chroma_backup.tar.gz data/chroma/

# 配置备份
tar -czf config_backup.tar.gz .env deploy/
```

### 数据恢复

```bash
# MySQL恢复
docker-compose exec -T mysql mysql -u root -p stock_cursor < backup.sql

# ChromaDB恢复
tar -xzf chroma_backup.tar.gz

# 配置恢复
tar -xzf config_backup.tar.gz
```

## 📝 维护指南

### 定期维护任务

1. **日志清理** (每周)
   ```bash
   find logs/ -name "*.log" -mtime +7 -delete
   ```

2. **数据库优化** (每月)
   ```sql
   OPTIMIZE TABLE stock_basic;
   ANALYZE TABLE stock_basic;
   ```

3. **系统更新** (每月)
   ```bash
   docker-compose pull
   docker-compose up -d
   ```

4. **备份检查** (每周)
   ```bash
   ./scripts/backup_check.sh
   ```

### 版本升级

```bash
# 1. 备份数据
./scripts/backup.sh

# 2. 拉取新版本
git pull origin main

# 3. 重新构建
docker-compose build --no-cache

# 4. 重启服务
docker-compose up -d

# 5. 验证升级
./scripts/health_check.sh
```

## 📞 技术支持

### 联系方式

- **GitHub Issues**: [项目Issues页面]
- **邮件支持**: <EMAIL>
- **文档**: [在线文档地址]

### 社区资源

- **用户手册**: docs/user-guide.md
- **开发指南**: docs/development-guide.md
- **API文档**: http://localhost/api/docs
- **常见问题**: docs/faq.md

---

**最后更新**: 2024-12-29  
**文档版本**: v1.0.0
