"""
RAG模块测试
"""
import pytest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from rag.retriever import StockRetriever
from rag.context_builder import <PERSON><PERSON><PERSON><PERSON>er
from vectorization.stock_vectorizer import StockVectorizer
from vectorization.vector_manager import VectorManager


class TestStockRetriever:
    """股票检索器测试"""
    
    @pytest.fixture
    def retriever(self):
        """创建检索器实例"""
        return StockRetriever()
    
    def test_retriever_initialization(self, retriever):
        """测试检索器初始化"""
        assert retriever is not None
        assert hasattr(retriever, 'vector_client')
        assert hasattr(retriever, 'mysql_client')
    
    def test_similarity_search(self, retriever):
        """测试相似度搜索"""
        # 测试基本搜索
        results = retriever.similarity_search(
            query="平安银行",
            top_k=5
        )
        
        assert results is not None
        assert isinstance(results, list)
        assert len(results) <= 5
    
    def test_hybrid_search(self, retriever):
        """测试混合搜索"""
        # 测试混合搜索（向量+关键词）
        results = retriever.hybrid_search(
            query="银行股票分析",
            top_k=3,
            alpha=0.7  # 向量搜索权重
        )
        
        assert results is not None
        assert isinstance(results, list)
        assert len(results) <= 3
    
    def test_filter_search(self, retriever):
        """测试过滤搜索"""
        # 测试带过滤条件的搜索
        results = retriever.search_with_filters(
            query="股票信息",
            filters={
                "type": "basic_info",
                "industry": "银行"
            },
            top_k=5
        )
        
        assert results is not None
        assert isinstance(results, list)
    
    def test_search_ranking(self, retriever):
        """测试搜索结果排序"""
        # 测试结果排序功能
        results = retriever.similarity_search(
            query="股票分析",
            top_k=10
        )
        
        if len(results) > 1:
            # 检查结果是否按相似度排序
            scores = [result.get('score', 0) for result in results]
            assert scores == sorted(scores, reverse=True)


class TestContextBuilder:
    """上下文构建器测试"""
    
    @pytest.fixture
    def context_builder(self):
        """创建上下文构建器实例"""
        return ContextBuilder()
    
    def test_context_builder_initialization(self, context_builder):
        """测试上下文构建器初始化"""
        assert context_builder is not None
        assert hasattr(context_builder, 'max_context_length')
        assert hasattr(context_builder, 'chunk_overlap')
    
    def test_build_context_from_documents(self, context_builder):
        """测试从文档构建上下文"""
        # 模拟检索结果
        mock_documents = [
            {
                "content": "平安银行是中国领先的股份制商业银行",
                "metadata": {"source": "basic_info", "ts_code": "000001.SZ"},
                "score": 0.95
            },
            {
                "content": "平安银行2023年净利润增长稳定",
                "metadata": {"source": "financial", "ts_code": "000001.SZ"},
                "score": 0.88
            }
        ]
        
        context = context_builder.build_context(
            query="平安银行分析",
            documents=mock_documents
        )
        
        assert context is not None
        assert isinstance(context, dict)
        assert "context_text" in context
        assert "sources" in context
        assert "metadata" in context
    
    def test_context_length_control(self, context_builder):
        """测试上下文长度控制"""
        # 创建长文档测试上下文长度控制
        long_documents = [
            {
                "content": "很长的文档内容 " * 1000,  # 创建很长的内容
                "metadata": {"source": "test"},
                "score": 0.9
            }
        ]
        
        context = context_builder.build_context(
            query="测试查询",
            documents=long_documents,
            max_length=500
        )
        
        assert context is not None
        assert len(context["context_text"]) <= 600  # 允许一些缓冲
    
    def test_multi_source_fusion(self, context_builder):
        """测试多源数据融合"""
        # 测试不同来源数据的融合
        multi_source_docs = [
            {
                "content": "基本信息：平安银行",
                "metadata": {"source": "basic_info", "type": "company"},
                "score": 0.9
            },
            {
                "content": "财务数据：营收增长",
                "metadata": {"source": "financial", "type": "performance"},
                "score": 0.85
            },
            {
                "content": "技术分析：股价趋势",
                "metadata": {"source": "technical", "type": "analysis"},
                "score": 0.8
            }
        ]
        
        context = context_builder.build_context(
            query="综合分析",
            documents=multi_source_docs
        )
        
        assert context is not None
        assert len(context["sources"]) == 3
        assert "basic_info" in [s["source"] for s in context["sources"]]
        assert "financial" in [s["source"] for s in context["sources"]]
        assert "technical" in [s["source"] for s in context["sources"]]


class TestStockVectorizer:
    """股票向量化器测试"""
    
    @pytest.fixture
    def vectorizer(self):
        """创建向量化器实例"""
        return StockVectorizer()
    
    def test_vectorizer_initialization(self, vectorizer):
        """测试向量化器初始化"""
        assert vectorizer is not None
        assert hasattr(vectorizer, 'embedding_model')
    
    def test_text_vectorization(self, vectorizer):
        """测试文本向量化"""
        test_text = "平安银行是一家优秀的股份制银行"
        
        vector = vectorizer.vectorize_text(test_text)
        
        assert vector is not None
        assert isinstance(vector, list)
        assert len(vector) > 0
        assert all(isinstance(x, (int, float)) for x in vector)
    
    def test_stock_data_vectorization(self, vectorizer):
        """测试股票数据向量化"""
        # 模拟股票数据
        stock_data = {
            "ts_code": "000001.SZ",
            "name": "平安银行",
            "industry": "银行",
            "price": 12.5,
            "pe_ratio": 8.5,
            "market_cap": 2500.0
        }
        
        vector = vectorizer.vectorize_stock_data(stock_data)
        
        assert vector is not None
        assert isinstance(vector, list)
        assert len(vector) > 0
    
    def test_batch_vectorization(self, vectorizer):
        """测试批量向量化"""
        test_texts = [
            "平安银行股票分析",
            "招商银行财务报告",
            "银行业投资建议"
        ]
        
        vectors = vectorizer.vectorize_batch(test_texts)
        
        assert vectors is not None
        assert isinstance(vectors, list)
        assert len(vectors) == len(test_texts)
        
        for vector in vectors:
            assert isinstance(vector, list)
            assert len(vector) > 0


class TestVectorManager:
    """向量管理器测试"""
    
    @pytest.fixture
    def vector_manager(self):
        """创建向量管理器实例"""
        return VectorManager()
    
    def test_vector_manager_initialization(self, vector_manager):
        """测试向量管理器初始化"""
        assert vector_manager is not None
        assert hasattr(vector_manager, 'vector_client')
        assert hasattr(vector_manager, 'vectorizer')
    
    def test_add_stock_vector(self, vector_manager):
        """测试添加股票向量"""
        stock_info = {
            "ts_code": "TEST001.SZ",
            "name": "测试股票",
            "industry": "测试行业",
            "description": "这是一个测试股票"
        }
        
        doc_id = vector_manager.add_stock_vector(
            stock_info=stock_info,
            vector_type="basic_info"
        )
        
        assert doc_id is not None
        assert "TEST001.SZ" in doc_id
        
        # 清理测试数据
        vector_manager.delete_vector(doc_id)
    
    def test_search_similar_stocks(self, vector_manager):
        """测试搜索相似股票"""
        results = vector_manager.search_similar_stocks(
            query="银行股票",
            top_k=5
        )
        
        assert results is not None
        assert isinstance(results, list)
        assert len(results) <= 5
    
    def test_update_stock_vector(self, vector_manager):
        """测试更新股票向量"""
        # 先添加一个测试向量
        stock_info = {
            "ts_code": "UPDATE001.SZ",
            "name": "更新测试股票",
            "industry": "测试行业"
        }
        
        doc_id = vector_manager.add_stock_vector(stock_info, "basic_info")
        
        # 更新向量
        updated_info = {
            "ts_code": "UPDATE001.SZ",
            "name": "已更新的测试股票",
            "industry": "新测试行业"
        }
        
        success = vector_manager.update_stock_vector(
            doc_id=doc_id,
            stock_info=updated_info,
            vector_type="basic_info"
        )
        
        assert success is True
        
        # 清理测试数据
        vector_manager.delete_vector(doc_id)


class TestRAGIntegration:
    """RAG集成测试"""
    
    def test_end_to_end_rag_flow(self):
        """测试端到端RAG流程"""
        # 1. 初始化组件
        retriever = StockRetriever()
        context_builder = ContextBuilder()
        
        # 2. 执行检索
        query = "平安银行投资分析"
        documents = retriever.similarity_search(query, top_k=5)
        
        # 3. 构建上下文
        context = context_builder.build_context(query, documents)
        
        # 4. 验证结果
        assert documents is not None
        assert context is not None
        assert "context_text" in context
        assert "sources" in context
    
    def test_rag_performance(self):
        """测试RAG性能"""
        import time
        
        retriever = StockRetriever()
        
        # 测试检索性能
        start_time = time.time()
        results = retriever.similarity_search("股票分析", top_k=10)
        retrieval_time = time.time() - start_time
        
        # 性能断言
        assert retrieval_time < 10.0  # 检索应在10秒内完成
        assert results is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
