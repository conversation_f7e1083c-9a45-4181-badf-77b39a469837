"""
集成测试 - 端到端功能测试
"""
import pytest
import requests
import time
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings

BASE_URL = f"http://localhost:{settings.api_port}"


class TestEndToEndWorkflows:
    """端到端工作流测试"""
    
    def test_complete_text2sql_workflow(self):
        """测试完整的Text2SQL工作流"""
        print("\n🔍 测试完整Text2SQL工作流...")
        
        # 1. 检查系统状态
        response = requests.get(f"{BASE_URL}/api/v1/system/status", timeout=10)
        assert response.status_code == 200
        
        system_data = response.json()
        assert system_data["success"] is True
        print("✅ 系统状态检查通过")
        
        # 2. 获取数据库模式信息
        response = requests.get(f"{BASE_URL}/api/v1/text2sql/schema", timeout=10)
        assert response.status_code == 200
        
        schema_data = response.json()
        assert schema_data["success"] is True
        print("✅ 数据库模式获取成功")
        
        # 3. 执行查询解释
        params = {"natural_query": "查询平安银行的基本信息"}
        response = requests.post(
            f"{BASE_URL}/api/v1/text2sql/explain",
            params=params,
            timeout=60
        )
        assert response.status_code == 200
        
        explain_data = response.json()
        assert explain_data["success"] is True
        print("✅ 查询解释成功")
        
        # 4. 执行实际查询
        payload = {
            "natural_query": "查询平安银行的基本信息",
            "format_type": "table",
            "execute": True,
            "limit": 5
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/text2sql/query",
            json=payload,
            timeout=120
        )
        assert response.status_code == 200
        
        query_data = response.json()
        assert query_data["success"] is True
        print("✅ Text2SQL查询执行成功")
        
        # 5. 验证结果格式
        result_data = query_data["data"]
        assert "generated_sql" in result_data
        assert "execution_time" in result_data
        print("✅ 查询结果格式正确")
    
    def test_complete_stock_analysis_workflow(self):
        """测试完整的股票分析工作流"""
        print("\n📊 测试完整股票分析工作流...")
        
        # 1. 获取股票概览
        params = {"stock_input": "000001"}
        response = requests.get(
            f"{BASE_URL}/api/v1/analysis/overview",
            params=params,
            timeout=30
        )
        assert response.status_code == 200
        
        overview_data = response.json()
        assert overview_data["success"] is True
        print("✅ 股票概览获取成功")
        
        # 2. 执行详细分析
        payload = {
            "stock_input": "000001",
            "analysis_types": ["基本面分析", "技术面分析"],
            "time_range": "3个月",
            "analysis_depth": 3
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/analysis/stock",
            json=payload,
            timeout=180
        )
        assert response.status_code == 200
        
        analysis_data = response.json()
        assert analysis_data["success"] is True
        print("✅ 股票分析执行成功")
    
    def test_rag_search_workflow(self):
        """测试RAG搜索工作流"""
        print("\n🔍 测试RAG搜索工作流...")
        
        # 1. 添加测试文档
        test_doc = {
            "content": "平安银行是中国领先的股份制商业银行，总部位于深圳",
            "metadata": {
                "source": "integration_test",
                "type": "company_info",
                "ts_code": "000001.SZ"
            },
            "doc_id": "integration_test_doc_001"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/rag/documents",
            json=test_doc,
            timeout=30
        )
        assert response.status_code == 200
        
        add_result = response.json()
        assert add_result["success"] is True
        print("✅ 测试文档添加成功")
        
        # 2. 执行搜索
        search_payload = {
            "query": "平安银行公司信息",
            "top_k": 5
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/rag/search",
            json=search_payload,
            timeout=30
        )
        assert response.status_code == 200
        
        search_result = response.json()
        assert search_result["success"] is True
        print("✅ RAG搜索执行成功")
        
        # 3. 清理测试文档
        response = requests.delete(
            f"{BASE_URL}/api/v1/rag/documents/integration_test_doc_001",
            timeout=10
        )
        assert response.status_code == 200
        print("✅ 测试文档清理完成")


class TestPerformanceTests:
    """性能测试"""
    
    def test_text2sql_performance(self):
        """测试Text2SQL性能"""
        print("\n⚡ 测试Text2SQL性能...")
        
        test_queries = [
            "查询平安银行的基本信息",
            "显示市值最大的5只股票",
            "查找银行板块的股票",
            "统计股票数量"
        ]
        
        total_time = 0
        successful_queries = 0
        
        for query in test_queries:
            payload = {
                "natural_query": query,
                "format_type": "table",
                "execute": True,
                "limit": 5
            }
            
            start_time = time.time()
            
            try:
                response = requests.post(
                    f"{BASE_URL}/api/v1/text2sql/query",
                    json=payload,
                    timeout=120
                )
                
                query_time = time.time() - start_time
                total_time += query_time
                
                if response.status_code == 200:
                    data = response.json()
                    if data["success"]:
                        successful_queries += 1
                        print(f"✅ 查询 '{query}' 完成，耗时: {query_time:.2f}s")
                    else:
                        print(f"❌ 查询 '{query}' 失败: {data.get('message', '未知错误')}")
                else:
                    print(f"❌ 查询 '{query}' HTTP错误: {response.status_code}")
                    
            except Exception as e:
                query_time = time.time() - start_time
                total_time += query_time
                print(f"❌ 查询 '{query}' 异常: {e}")
        
        # 性能断言
        avg_time = total_time / len(test_queries)
        success_rate = successful_queries / len(test_queries)
        
        print(f"📊 性能统计:")
        print(f"  - 平均查询时间: {avg_time:.2f}s")
        print(f"  - 成功率: {success_rate:.1%}")
        print(f"  - 总耗时: {total_time:.2f}s")
        
        assert avg_time < 90.0  # 平均查询时间应小于90秒
        assert success_rate >= 0.5  # 成功率应大于50%
    
    def test_concurrent_requests_performance(self):
        """测试并发请求性能"""
        print("\n🚀 测试并发请求性能...")
        
        import threading
        
        results = []
        
        def make_health_request():
            try:
                start_time = time.time()
                response = requests.get(f"{BASE_URL}/health", timeout=10)
                request_time = time.time() - start_time
                
                results.append({
                    "success": response.status_code == 200,
                    "time": request_time
                })
            except Exception as e:
                results.append({
                    "success": False,
                    "time": 10.0,
                    "error": str(e)
                })
        
        # 创建20个并发请求
        threads = []
        for _ in range(20):
            thread = threading.Thread(target=make_health_request)
            threads.append(thread)
        
        start_time = time.time()
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 分析结果
        successful_requests = sum(1 for r in results if r["success"])
        avg_response_time = sum(r["time"] for r in results) / len(results)
        
        print(f"📊 并发测试结果:")
        print(f"  - 并发请求数: {len(results)}")
        print(f"  - 成功请求数: {successful_requests}")
        print(f"  - 成功率: {successful_requests/len(results):.1%}")
        print(f"  - 平均响应时间: {avg_response_time:.3f}s")
        print(f"  - 总耗时: {total_time:.2f}s")
        
        assert successful_requests >= 18  # 至少90%成功
        assert avg_response_time < 5.0  # 平均响应时间小于5秒
        assert total_time < 30.0  # 总时间小于30秒
    
    def test_memory_usage_stability(self):
        """测试内存使用稳定性"""
        print("\n💾 测试内存使用稳定性...")
        
        # 连续发送多个请求，检查系统稳定性
        for i in range(10):
            response = requests.get(f"{BASE_URL}/api/v1/system/status", timeout=15)
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            
            if i % 3 == 0:
                print(f"✅ 第 {i+1} 次请求完成")
            
            time.sleep(1)  # 短暂延迟
        
        print("✅ 内存使用稳定性测试通过")


class TestErrorHandlingIntegration:
    """错误处理集成测试"""
    
    def test_database_connection_failure_handling(self):
        """测试数据库连接失败处理"""
        print("\n🔧 测试错误处理...")
        
        # 测试无效查询的处理
        payload = {
            "natural_query": "这是一个无法转换为SQL的查询",
            "format_type": "table",
            "execute": True
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/text2sql/query",
            json=payload,
            timeout=60
        )
        
        # 应该返回错误信息而不是崩溃
        assert response.status_code in [200, 400, 422]
        
        if response.status_code == 200:
            data = response.json()
            # 如果返回200，应该有错误信息
            if not data["success"]:
                assert "message" in data
        
        print("✅ 无效查询错误处理正常")
    
    def test_timeout_handling(self):
        """测试超时处理"""
        print("\n⏰ 测试超时处理...")
        
        # 使用较短的超时时间测试
        try:
            response = requests.get(
                f"{BASE_URL}/api/v1/system/status",
                timeout=0.001  # 极短超时
            )
        except requests.exceptions.Timeout:
            print("✅ 超时异常正确抛出")
        except Exception as e:
            print(f"✅ 其他网络异常: {type(e).__name__}")
    
    def test_invalid_input_handling(self):
        """测试无效输入处理"""
        print("\n❌ 测试无效输入处理...")
        
        # 测试空查询
        payload = {
            "natural_query": "",
            "format_type": "table",
            "execute": True
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/text2sql/query",
            json=payload,
            timeout=30
        )
        
        assert response.status_code in [200, 400, 422]
        print("✅ 空查询处理正常")
        
        # 测试无效股票代码
        params = {"stock_input": "INVALID_STOCK_CODE_12345"}
        response = requests.get(
            f"{BASE_URL}/api/v1/analysis/overview",
            params=params,
            timeout=30
        )
        
        assert response.status_code in [200, 400, 404]
        print("✅ 无效股票代码处理正常")


class TestDataConsistency:
    """数据一致性测试"""
    
    def test_cross_module_data_consistency(self):
        """测试跨模块数据一致性"""
        print("\n🔄 测试数据一致性...")
        
        # 1. 通过Text2SQL查询股票信息
        payload = {
            "natural_query": "查询平安银行的基本信息",
            "format_type": "table",
            "execute": True,
            "limit": 1
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/text2sql/query",
            json=payload,
            timeout=120
        )
        
        if response.status_code == 200:
            text2sql_data = response.json()
            if text2sql_data["success"]:
                print("✅ Text2SQL查询成功")
                
                # 2. 通过分析接口查询同一股票
                params = {"stock_input": "000001"}
                response = requests.get(
                    f"{BASE_URL}/api/v1/analysis/overview",
                    params=params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    analysis_data = response.json()
                    if analysis_data["success"]:
                        print("✅ 股票分析查询成功")
                        print("✅ 跨模块数据一致性验证通过")
                    else:
                        print("⚠️  股票分析查询失败，但不影响一致性测试")
                else:
                    print("⚠️  股票分析接口错误，但不影响一致性测试")
            else:
                print("⚠️  Text2SQL查询失败，跳过一致性测试")
        else:
            print("⚠️  Text2SQL接口错误，跳过一致性测试")


if __name__ == "__main__":
    # 确保API服务正在运行
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ API服务未运行，请先启动API服务")
            exit(1)
        print("✅ API服务运行正常，开始集成测试")
    except:
        print("❌ 无法连接到API服务，请确保服务正在运行")
        exit(1)
    
    pytest.main([__file__, "-v", "-s"])
