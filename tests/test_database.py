"""
数据库操作测试
"""
import pytest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.mysql_client import mysql_client
from database.vector_client import vector_client
from config.settings import settings


class TestMySQLClient:
    """MySQL客户端测试"""
    
    def test_mysql_initialization(self):
        """测试MySQL初始化"""
        # 重新初始化以确保测试环境
        mysql_client._initialized = False
        mysql_client._engine = None
        
        mysql_client.initialize()
        assert mysql_client._initialized is True
        assert mysql_client._engine is not None
    
    def test_mysql_connection(self):
        """测试MySQL连接"""
        result = mysql_client.test_connection()
        assert result is True
    
    def test_mysql_query_execution(self):
        """测试MySQL查询执行"""
        # 测试简单查询
        result = mysql_client.execute_query("SELECT 1 as test_value")
        assert result is not None
        assert len(result) == 1
        assert result[0]["test_value"] == 1
    
    def test_mysql_database_info(self):
        """测试数据库信息查询"""
        result = mysql_client.execute_query("SELECT DATABASE() as db_name, VERSION() as version")
        assert result is not None
        assert len(result) == 1
        assert result[0]["db_name"] == settings.mysql_database
        assert "." in result[0]["version"]  # 版本号包含点号
    
    def test_mysql_table_list(self):
        """测试表列表查询"""
        result = mysql_client.execute_query("SHOW TABLES")
        assert result is not None
        assert len(result) > 0  # 应该有表存在
    
    def test_mysql_stock_basic_table(self):
        """测试股票基本信息表"""
        # 检查表结构
        result = mysql_client.execute_query("DESCRIBE stock_basic")
        assert result is not None
        assert len(result) > 0
        
        # 检查必要字段
        columns = [row["Field"] for row in result]
        required_columns = ["ts_code", "name", "industry"]
        for col in required_columns:
            assert col in columns
    
    def test_mysql_stock_data_query(self):
        """测试股票数据查询"""
        # 查询股票基本信息
        result = mysql_client.execute_query(
            "SELECT ts_code, name, industry FROM stock_basic LIMIT 5"
        )
        assert result is not None
        # 可能没有数据，但查询应该成功
        if result:
            assert len(result) <= 5
            for row in result:
                assert "ts_code" in row
                assert "name" in row
    
    def test_mysql_error_handling(self):
        """测试错误处理"""
        # 测试无效SQL
        result = mysql_client.execute_query("SELECT * FROM non_existent_table")
        assert result is None  # 应该返回None而不是抛出异常


class TestVectorClient:
    """向量数据库客户端测试"""
    
    def test_vector_initialization(self):
        """测试向量数据库初始化"""
        vector_client.initialize()
        assert vector_client._client is not None
        assert vector_client._collection is not None
    
    def test_vector_collection_info(self):
        """测试集合信息获取"""
        info = vector_client.get_collection_info()
        assert info is not None
        assert "name" in info
        assert "count" in info
        assert info["name"] == settings.chroma_collection_name
    
    def test_vector_add_document(self):
        """测试添加文档"""
        test_doc = {
            "content": "测试文档内容",
            "metadata": {
                "source": "test",
                "type": "unit_test",
                "timestamp": "2024-12-29"
            }
        }
        
        # 添加文档
        doc_id = vector_client.add_document(
            content=test_doc["content"],
            metadata=test_doc["metadata"],
            doc_id="test_doc_001"
        )
        
        assert doc_id == "test_doc_001"
    
    def test_vector_query_documents(self):
        """测试文档查询"""
        # 查询文档
        results = vector_client.query_documents(
            query_text="测试文档",
            top_k=5
        )
        
        assert results is not None
        assert isinstance(results, list)
        # 可能没有结果，但查询应该成功
    
    def test_vector_delete_document(self):
        """测试删除文档"""
        # 删除测试文档
        success = vector_client.delete_document("test_doc_001")
        # 无论文档是否存在，删除操作都应该成功
        assert success is True
    
    def test_vector_batch_operations(self):
        """测试批量操作"""
        # 准备测试数据
        test_docs = [
            {
                "id": f"batch_test_{i}",
                "content": f"批量测试文档 {i}",
                "metadata": {"batch": "test", "index": i}
            }
            for i in range(3)
        ]
        
        # 批量添加
        doc_ids = vector_client.add_documents([
            {
                "content": doc["content"],
                "metadata": doc["metadata"],
                "doc_id": doc["id"]
            }
            for doc in test_docs
        ])
        
        assert len(doc_ids) == 3
        
        # 清理测试数据
        for doc in test_docs:
            vector_client.delete_document(doc["id"])


class TestDatabaseIntegration:
    """数据库集成测试"""
    
    def test_database_connectivity(self):
        """测试数据库连通性"""
        # MySQL连接测试
        mysql_ok = mysql_client.test_connection()
        assert mysql_ok is True
        
        # 向量数据库连接测试
        vector_info = vector_client.get_collection_info()
        assert vector_info is not None
    
    def test_cross_database_operations(self):
        """测试跨数据库操作"""
        # 从MySQL获取股票信息
        mysql_result = mysql_client.execute_query(
            "SELECT ts_code, name FROM stock_basic LIMIT 1"
        )
        
        if mysql_result and len(mysql_result) > 0:
            stock_info = mysql_result[0]
            
            # 将信息添加到向量数据库
            doc_content = f"股票代码: {stock_info['ts_code']}, 股票名称: {stock_info['name']}"
            doc_id = vector_client.add_document(
                content=doc_content,
                metadata={
                    "source": "mysql",
                    "ts_code": stock_info["ts_code"],
                    "type": "stock_info"
                },
                doc_id=f"test_stock_{stock_info['ts_code']}"
            )
            
            assert doc_id is not None
            
            # 清理测试数据
            vector_client.delete_document(doc_id)
    
    def test_database_performance(self):
        """测试数据库性能"""
        import time
        
        # MySQL查询性能测试
        start_time = time.time()
        mysql_client.execute_query("SELECT COUNT(*) as count FROM stock_basic")
        mysql_time = time.time() - start_time
        
        # 向量数据库查询性能测试
        start_time = time.time()
        vector_client.get_collection_info()
        vector_time = time.time() - start_time
        
        # 性能断言（应该在合理时间内完成）
        assert mysql_time < 5.0  # MySQL查询应在5秒内完成
        assert vector_time < 3.0  # 向量数据库操作应在3秒内完成


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
