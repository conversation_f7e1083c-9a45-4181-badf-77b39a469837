"""
Text2SQL模块测试
"""
import pytest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from text2sql.query_processor import QueryProcessor
from text2sql.sql_generator import SQLGenerator
from text2sql.result_formatter import ResultFormatter
from text2sql.schema_manager import SchemaManager


class TestQueryProcessor:
    """查询处理器测试"""
    
    @pytest.fixture
    def processor(self):
        """创建查询处理器实例"""
        return QueryProcessor()
    
    def test_processor_initialization(self, processor):
        """测试处理器初始化"""
        assert processor is not None
        assert hasattr(processor, 'nlp_model')
    
    def test_query_preprocessing(self, processor):
        """测试查询预处理"""
        raw_query = "  查询平安银行的基本信息  "
        processed = processor.preprocess_query(raw_query)
        
        assert processed is not None
        assert processed.strip() == "查询平安银行的基本信息"
        assert isinstance(processed, str)
    
    def test_intent_recognition(self, processor):
        """测试意图识别"""
        test_queries = [
            ("查询平安银行的基本信息", "basic_info"),
            ("显示市值最大的10只股票", "ranking"),
            ("查找银行板块的股票", "filter"),
            ("统计股票数量", "aggregation")
        ]
        
        for query, expected_intent in test_queries:
            intent = processor.recognize_intent(query)
            assert intent is not None
            # 意图识别可能不完全准确，但应该返回有效结果
            assert isinstance(intent, str)
    
    def test_entity_extraction(self, processor):
        """测试实体提取"""
        query = "查询平安银行和招商银行的股价信息"
        entities = processor.extract_entities(query)
        
        assert entities is not None
        assert isinstance(entities, dict)
        
        # 检查是否提取到股票名称
        if "stocks" in entities:
            assert isinstance(entities["stocks"], list)
    
    def test_query_analysis(self, processor):
        """测试查询分析"""
        query = "查询平安银行的基本信息"
        analysis = processor.analyze_query(query)
        
        assert analysis is not None
        assert isinstance(analysis, dict)
        assert "intent" in analysis
        assert "entities" in analysis
        assert "query_type" in analysis


class TestSQLGenerator:
    """SQL生成器测试"""
    
    @pytest.fixture
    def generator(self):
        """创建SQL生成器实例"""
        return SQLGenerator()
    
    def test_generator_initialization(self, generator):
        """测试生成器初始化"""
        assert generator is not None
        assert hasattr(generator, 'llm_client')
        assert hasattr(generator, 'schema_manager')
    
    def test_basic_sql_generation(self, generator):
        """测试基本SQL生成"""
        query = "查询平安银行的基本信息"
        
        sql_result = generator.generate_sql(query)
        
        assert sql_result is not None
        assert isinstance(sql_result, dict)
        assert "sql" in sql_result
        assert "confidence" in sql_result
        
        # 检查生成的SQL
        sql = sql_result["sql"]
        assert isinstance(sql, str)
        assert "SELECT" in sql.upper()
    
    def test_complex_sql_generation(self, generator):
        """测试复杂SQL生成"""
        complex_queries = [
            "显示市值最大的10只股票",
            "查找银行板块的所有股票",
            "统计每个行业的股票数量",
            "查询PE比率小于20的股票"
        ]
        
        for query in complex_queries:
            sql_result = generator.generate_sql(query)
            
            assert sql_result is not None
            assert "sql" in sql_result
            
            sql = sql_result["sql"]
            assert isinstance(sql, str)
            assert len(sql.strip()) > 0
    
    def test_sql_validation(self, generator):
        """测试SQL验证"""
        valid_sql = "SELECT * FROM stock_basic LIMIT 10"
        invalid_sql = "INVALID SQL STATEMENT"
        
        # 测试有效SQL
        is_valid = generator.validate_sql(valid_sql)
        assert is_valid is True
        
        # 测试无效SQL
        is_valid = generator.validate_sql(invalid_sql)
        assert is_valid is False
    
    def test_sql_optimization(self, generator):
        """测试SQL优化"""
        original_sql = "SELECT * FROM stock_basic WHERE name LIKE '%银行%'"
        optimized = generator.optimize_sql(original_sql)
        
        assert optimized is not None
        assert isinstance(optimized, str)
        # 优化后的SQL应该仍然有效
        assert "SELECT" in optimized.upper()


class TestResultFormatter:
    """结果格式化器测试"""
    
    @pytest.fixture
    def formatter(self):
        """创建结果格式化器实例"""
        return ResultFormatter()
    
    def test_formatter_initialization(self, formatter):
        """测试格式化器初始化"""
        assert formatter is not None
    
    def test_table_formatting(self, formatter):
        """测试表格格式化"""
        # 模拟查询结果
        mock_data = [
            {"ts_code": "000001.SZ", "name": "平安银行", "industry": "银行"},
            {"ts_code": "000002.SZ", "name": "万科A", "industry": "房地产"}
        ]
        
        formatted = formatter.format_as_table(mock_data)
        
        assert formatted is not None
        assert isinstance(formatted, dict)
        assert "data" in formatted
        assert "columns" in formatted
        assert "total_rows" in formatted
    
    def test_json_formatting(self, formatter):
        """测试JSON格式化"""
        mock_data = [
            {"ts_code": "000001.SZ", "name": "平安银行"},
            {"ts_code": "000002.SZ", "name": "万科A"}
        ]
        
        formatted = formatter.format_as_json(mock_data)
        
        assert formatted is not None
        assert isinstance(formatted, dict)
        assert "data" in formatted
        assert "format" in formatted
        assert formatted["format"] == "json"
    
    def test_csv_formatting(self, formatter):
        """测试CSV格式化"""
        mock_data = [
            {"ts_code": "000001.SZ", "name": "平安银行"},
            {"ts_code": "000002.SZ", "name": "万科A"}
        ]
        
        formatted = formatter.format_as_csv(mock_data)
        
        assert formatted is not None
        assert isinstance(formatted, dict)
        assert "data" in formatted
        assert "format" in formatted
        assert formatted["format"] == "csv"
    
    def test_empty_result_formatting(self, formatter):
        """测试空结果格式化"""
        empty_data = []
        
        # 测试各种格式的空结果处理
        table_result = formatter.format_as_table(empty_data)
        json_result = formatter.format_as_json(empty_data)
        csv_result = formatter.format_as_csv(empty_data)
        
        assert table_result is not None
        assert json_result is not None
        assert csv_result is not None
        
        assert table_result["total_rows"] == 0
        assert len(json_result["data"]) == 0
        assert csv_result["data"] == ""


class TestSchemaManager:
    """数据库模式管理器测试"""
    
    @pytest.fixture
    def schema_manager(self):
        """创建模式管理器实例"""
        return SchemaManager()
    
    def test_schema_manager_initialization(self, schema_manager):
        """测试模式管理器初始化"""
        assert schema_manager is not None
        assert hasattr(schema_manager, 'mysql_client')
    
    def test_get_table_schema(self, schema_manager):
        """测试获取表结构"""
        # 测试获取stock_basic表结构
        schema = schema_manager.get_table_schema("stock_basic")
        
        assert schema is not None
        assert isinstance(schema, dict)
        assert "columns" in schema
        assert "table_name" in schema
        
        # 检查必要字段
        columns = schema["columns"]
        assert isinstance(columns, list)
        if columns:
            assert "name" in columns[0]
            assert "type" in columns[0]
    
    def test_get_all_tables(self, schema_manager):
        """测试获取所有表"""
        tables = schema_manager.get_all_tables()
        
        assert tables is not None
        assert isinstance(tables, list)
        assert len(tables) > 0
        
        # 检查是否包含预期的表
        table_names = [table["name"] for table in tables]
        assert "stock_basic" in table_names
    
    def test_get_table_relationships(self, schema_manager):
        """测试获取表关系"""
        relationships = schema_manager.get_table_relationships()
        
        assert relationships is not None
        assert isinstance(relationships, dict)
    
    def test_generate_schema_description(self, schema_manager):
        """测试生成模式描述"""
        description = schema_manager.generate_schema_description()
        
        assert description is not None
        assert isinstance(description, str)
        assert len(description) > 0
        assert "stock_basic" in description


class TestText2SQLIntegration:
    """Text2SQL集成测试"""
    
    def test_end_to_end_text2sql(self):
        """测试端到端Text2SQL流程"""
        # 1. 初始化组件
        processor = QueryProcessor()
        generator = SQLGenerator()
        formatter = ResultFormatter()
        
        # 2. 处理查询
        query = "查询平安银行的基本信息"
        
        # 3. 分析查询
        analysis = processor.analyze_query(query)
        assert analysis is not None
        
        # 4. 生成SQL
        sql_result = generator.generate_sql(query)
        assert sql_result is not None
        assert "sql" in sql_result
        
        # 5. 验证SQL
        is_valid = generator.validate_sql(sql_result["sql"])
        assert is_valid is True
    
    def test_text2sql_performance(self):
        """测试Text2SQL性能"""
        import time
        
        generator = SQLGenerator()
        
        # 测试SQL生成性能
        start_time = time.time()
        sql_result = generator.generate_sql("查询股票基本信息")
        generation_time = time.time() - start_time
        
        # 性能断言
        assert generation_time < 60.0  # SQL生成应在60秒内完成
        assert sql_result is not None
    
    def test_text2sql_error_handling(self):
        """测试Text2SQL错误处理"""
        generator = SQLGenerator()
        
        # 测试空查询
        result = generator.generate_sql("")
        assert result is not None
        # 应该有错误处理机制
        
        # 测试无效查询
        result = generator.generate_sql("这不是一个有效的查询")
        assert result is not None
        # 应该返回错误信息或默认结果


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
