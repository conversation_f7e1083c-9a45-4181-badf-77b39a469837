"""
API接口测试
"""
import pytest
import requests
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings

# API基础URL
BASE_URL = f"http://localhost:{settings.api_port}"


class TestHealthAPI:
    """健康检查API测试"""
    
    def test_health_check(self):
        """测试健康检查接口"""
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
    
    def test_health_check_response_time(self):
        """测试健康检查响应时间"""
        import time
        
        start_time = time.time()
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        response_time = time.time() - start_time
        
        assert response.status_code == 200
        assert response_time < 2.0  # 应在2秒内响应


class TestSystemAPI:
    """系统状态API测试"""
    
    def test_system_status(self):
        """测试系统状态接口"""
        response = requests.get(f"{BASE_URL}/api/v1/system/status", timeout=15)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "success" in data
        assert "data" in data
        assert data["success"] is True
        
        # 检查状态数据结构
        status_data = data["data"]
        assert "status" in status_data
        assert "statistics" in status_data
        assert "timestamp" in status_data
    
    def test_system_info(self):
        """测试系统信息接口"""
        response = requests.get(f"{BASE_URL}/api/v1/system/info", timeout=10)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "success" in data
        assert "data" in data
        
        info_data = data["data"]
        assert "version" in info_data
        assert "environment" in info_data


class TestText2SQLAPI:
    """Text2SQL API测试"""
    
    def test_text2sql_query_basic(self):
        """测试基本Text2SQL查询"""
        payload = {
            "natural_query": "查询平安银行的基本信息",
            "format_type": "table",
            "execute": True,
            "limit": 5
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/text2sql/query",
            json=payload,
            timeout=120
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "success" in data
        assert "data" in data
        
        if data["success"]:
            result_data = data["data"]
            assert "generated_sql" in result_data
            assert "execution_time" in result_data
    
    def test_text2sql_query_formats(self):
        """测试不同输出格式"""
        formats = ["table", "json", "csv"]
        
        for format_type in formats:
            payload = {
                "natural_query": "查询股票基本信息",
                "format_type": format_type,
                "execute": True,
                "limit": 3
            }
            
            response = requests.post(
                f"{BASE_URL}/api/v1/text2sql/query",
                json=payload,
                timeout=90
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "success" in data
    
    def test_text2sql_explain(self):
        """测试查询解释接口"""
        params = {
            "natural_query": "查询平安银行的基本信息"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/text2sql/explain",
            params=params,
            timeout=60
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "success" in data
        assert "data" in data
        
        if data["success"]:
            explain_data = data["data"]
            assert "intent" in explain_data
            assert "generated_sql" in explain_data
    
    def test_text2sql_invalid_query(self):
        """测试无效查询处理"""
        payload = {
            "natural_query": "",
            "format_type": "table",
            "execute": True
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/text2sql/query",
            json=payload,
            timeout=30
        )
        
        # 应该返回错误或处理空查询
        assert response.status_code in [200, 400, 422]
    
    def test_text2sql_schema_info(self):
        """测试数据库模式信息接口"""
        response = requests.get(
            f"{BASE_URL}/api/v1/text2sql/schema",
            timeout=10
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "success" in data
        assert "data" in data
        
        if data["success"]:
            schema_data = data["data"]
            assert "tables" in schema_data
            assert isinstance(schema_data["tables"], list)


class TestAnalysisAPI:
    """股票分析API测试"""
    
    def test_stock_overview(self):
        """测试股票概览接口"""
        params = {
            "stock_input": "000001"
        }
        
        response = requests.get(
            f"{BASE_URL}/api/v1/analysis/overview",
            params=params,
            timeout=30
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "success" in data
        assert "data" in data
    
    def test_stock_analysis(self):
        """测试股票分析接口"""
        payload = {
            "stock_input": "000001",
            "analysis_types": ["基本面分析", "技术面分析"],
            "time_range": "6个月",
            "analysis_depth": 3
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/analysis/stock",
            json=payload,
            timeout=180
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "success" in data
        assert "data" in data
    
    def test_analysis_invalid_stock(self):
        """测试无效股票代码处理"""
        params = {
            "stock_input": "INVALID_CODE"
        }
        
        response = requests.get(
            f"{BASE_URL}/api/v1/analysis/overview",
            params=params,
            timeout=30
        )
        
        # 应该返回错误或空结果
        assert response.status_code in [200, 400, 404]


class TestRAGAPI:
    """RAG搜索API测试"""
    
    def test_rag_search(self):
        """测试RAG搜索接口"""
        payload = {
            "query": "平安银行投资分析",
            "top_k": 5
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/rag/search",
            json=payload,
            timeout=30
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "success" in data
        assert "data" in data
        
        if data["success"]:
            search_data = data["data"]
            assert "results" in search_data
            assert isinstance(search_data["results"], list)
    
    def test_rag_add_document(self):
        """测试添加文档接口"""
        payload = {
            "content": "测试文档内容：这是一个API测试文档",
            "metadata": {
                "source": "api_test",
                "type": "test_document",
                "timestamp": "2024-12-29"
            },
            "doc_id": "api_test_doc_001"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/rag/documents",
            json=payload,
            timeout=30
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "success" in data
        
        # 清理测试文档
        if data["success"]:
            delete_response = requests.delete(
                f"{BASE_URL}/api/v1/rag/documents/api_test_doc_001",
                timeout=10
            )
            assert delete_response.status_code == 200


class TestAPIPerformance:
    """API性能测试"""
    
    def test_concurrent_requests(self):
        """测试并发请求"""
        import threading
        import time
        
        results = []
        
        def make_request():
            try:
                response = requests.get(f"{BASE_URL}/health", timeout=10)
                results.append(response.status_code == 200)
            except:
                results.append(False)
        
        # 创建10个并发请求
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
        
        start_time = time.time()
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 检查结果
        assert len(results) == 10
        assert all(results)  # 所有请求都应该成功
        assert total_time < 30.0  # 总时间应在30秒内
    
    def test_api_response_times(self):
        """测试API响应时间"""
        import time
        
        endpoints = [
            "/health",
            "/api/v1/system/status",
            "/api/v1/system/info"
        ]
        
        for endpoint in endpoints:
            start_time = time.time()
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=15)
            response_time = time.time() - start_time
            
            assert response.status_code == 200
            assert response_time < 10.0  # 每个接口应在10秒内响应


class TestAPIErrorHandling:
    """API错误处理测试"""
    
    def test_invalid_endpoints(self):
        """测试无效端点"""
        response = requests.get(f"{BASE_URL}/api/v1/invalid/endpoint", timeout=10)
        assert response.status_code == 404
    
    def test_invalid_methods(self):
        """测试无效HTTP方法"""
        # 对只支持GET的端点发送POST请求
        response = requests.post(f"{BASE_URL}/health", timeout=10)
        assert response.status_code in [405, 422]  # Method Not Allowed 或 Unprocessable Entity
    
    def test_invalid_json_payload(self):
        """测试无效JSON负载"""
        invalid_json = "{ invalid json }"
        
        response = requests.post(
            f"{BASE_URL}/api/v1/text2sql/query",
            data=invalid_json,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        assert response.status_code in [400, 422]  # Bad Request 或 Unprocessable Entity
    
    def test_missing_required_fields(self):
        """测试缺少必需字段"""
        payload = {
            # 缺少natural_query字段
            "format_type": "table"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/text2sql/query",
            json=payload,
            timeout=10
        )
        
        assert response.status_code in [400, 422]


if __name__ == "__main__":
    # 确保API服务正在运行
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print("❌ API服务未运行，请先启动API服务")
            exit(1)
    except:
        print("❌ 无法连接到API服务，请确保服务正在运行")
        exit(1)
    
    pytest.main([__file__, "-v"])
