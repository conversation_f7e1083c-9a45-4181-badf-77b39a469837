#!/usr/bin/env python3
"""
测试运行脚本
"""
import subprocess
import sys
import time
import requests
from pathlib import Path

def check_api_service():
    """检查API服务是否运行"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def run_unit_tests():
    """运行单元测试"""
    print("=" * 60)
    print("🧪 运行单元测试")
    print("=" * 60)
    
    test_files = [
        "tests/test_database.py",
        "tests/test_rag.py", 
        "tests/test_text2sql.py",
        "tests/test_api.py"
    ]
    
    results = {}
    
    for test_file in test_files:
        print(f"\n📋 运行 {test_file}...")
        
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pytest", test_file, "-v", "--tb=short"],
                capture_output=True,
                text=True,
                timeout=300
            )
            
            results[test_file] = {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr
            }
            
            if result.returncode == 0:
                print(f"✅ {test_file} 测试通过")
            else:
                print(f"❌ {test_file} 测试失败")
                print(f"错误输出: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_file} 测试超时")
            results[test_file] = {
                "success": False,
                "output": "",
                "error": "测试超时"
            }
        except Exception as e:
            print(f"❌ {test_file} 测试异常: {e}")
            results[test_file] = {
                "success": False,
                "output": "",
                "error": str(e)
            }
    
    return results

def run_integration_tests():
    """运行集成测试"""
    print("\n" + "=" * 60)
    print("🔗 运行集成测试")
    print("=" * 60)
    
    if not check_api_service():
        print("❌ API服务未运行，跳过集成测试")
        return {"tests/test_integration.py": {"success": False, "error": "API服务未运行"}}
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pytest", "tests/test_integration.py", "-v", "-s", "--tb=short"],
            capture_output=True,
            text=True,
            timeout=600  # 集成测试需要更长时间
        )
        
        integration_result = {
            "success": result.returncode == 0,
            "output": result.stdout,
            "error": result.stderr
        }
        
        if result.returncode == 0:
            print("✅ 集成测试通过")
        else:
            print("❌ 集成测试失败")
            print(f"错误输出: {result.stderr}")
        
        return {"tests/test_integration.py": integration_result}
        
    except subprocess.TimeoutExpired:
        print("⏰ 集成测试超时")
        return {"tests/test_integration.py": {"success": False, "error": "测试超时"}}
    except Exception as e:
        print(f"❌ 集成测试异常: {e}")
        return {"tests/test_integration.py": {"success": False, "error": str(e)}}

def generate_test_report(unit_results, integration_results):
    """生成测试报告"""
    print("\n" + "=" * 60)
    print("📊 测试报告")
    print("=" * 60)
    
    all_results = {**unit_results, **integration_results}
    
    total_tests = len(all_results)
    passed_tests = sum(1 for result in all_results.values() if result["success"])
    
    print(f"\n📋 测试概览:")
    print(f"  - 总测试文件: {total_tests}")
    print(f"  - 通过测试: {passed_tests}")
    print(f"  - 失败测试: {total_tests - passed_tests}")
    print(f"  - 成功率: {passed_tests/total_tests:.1%}")
    
    print(f"\n📝 详细结果:")
    for test_file, result in all_results.items():
        status = "✅ 通过" if result["success"] else "❌ 失败"
        print(f"  - {test_file}: {status}")
        
        if not result["success"] and result.get("error"):
            print(f"    错误: {result['error']}")
    
    # 生成HTML报告
    generate_html_report(all_results)
    
    return passed_tests == total_tests

def generate_html_report(results):
    """生成HTML测试报告"""
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>股票分析RAG系统 - 测试报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f8ff; padding: 20px; border-radius: 10px; }
        .summary { background: #f9f9f9; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .test-item { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }
        .passed { border-left-color: #28a745; background: #d4edda; }
        .failed { border-left-color: #dc3545; background: #f8d7da; }
        .error { color: #721c24; font-family: monospace; font-size: 12px; }
        .timestamp { color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 股票分析RAG系统 - 测试报告</h1>
        <p class="timestamp">生成时间: {timestamp}</p>
    </div>
    
    <div class="summary">
        <h2>📊 测试概览</h2>
        <p><strong>总测试文件:</strong> {total}</p>
        <p><strong>通过测试:</strong> {passed}</p>
        <p><strong>失败测试:</strong> {failed}</p>
        <p><strong>成功率:</strong> {success_rate:.1%}</p>
    </div>
    
    <div class="details">
        <h2>📝 详细结果</h2>
        {test_details}
    </div>
</body>
</html>
    """
    
    total = len(results)
    passed = sum(1 for r in results.values() if r["success"])
    failed = total - passed
    success_rate = passed / total if total > 0 else 0
    
    test_details = ""
    for test_file, result in results.items():
        status_class = "passed" if result["success"] else "failed"
        status_text = "✅ 通过" if result["success"] else "❌ 失败"
        
        error_html = ""
        if not result["success"] and result.get("error"):
            error_html = f'<div class="error">错误: {result["error"]}</div>'
        
        test_details += f"""
        <div class="test-item {status_class}">
            <h3>{test_file}</h3>
            <p><strong>状态:</strong> {status_text}</p>
            {error_html}
        </div>
        """
    
    final_html = html_content.format(
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
        total=total,
        passed=passed,
        failed=failed,
        success_rate=success_rate,
        test_details=test_details
    )
    
    # 保存HTML报告
    report_path = Path("test_report.html")
    with open(report_path, "w", encoding="utf-8") as f:
        f.write(final_html)
    
    print(f"\n📄 HTML测试报告已生成: {report_path.absolute()}")

def main():
    """主函数"""
    print("🚀 开始运行股票分析RAG系统测试套件")
    print(f"📁 工作目录: {Path.cwd()}")
    
    # 检查测试环境
    print("\n🔍 检查测试环境...")
    
    # 检查pytest是否安装
    try:
        import pytest
        print("✅ pytest 已安装")
    except ImportError:
        print("❌ pytest 未安装，请运行: pip install pytest")
        return False
    
    # 检查API服务
    if check_api_service():
        print("✅ API服务运行正常")
    else:
        print("⚠️  API服务未运行，将跳过API相关测试")
    
    # 运行单元测试
    unit_results = run_unit_tests()
    
    # 运行集成测试
    integration_results = run_integration_tests()
    
    # 生成测试报告
    all_passed = generate_test_report(unit_results, integration_results)
    
    if all_passed:
        print("\n🎉 所有测试通过！系统质量良好")
        return True
    else:
        print("\n⚠️  部分测试失败，请检查问题并修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
