"""
SQL生成器模块
"""
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date, timedelta

from llm.model_manager import model_manager, ModelType
from text2sql.schema_parser import schema_parser
from text2sql.query_validator import query_validator
from utils.logger import get_logger

logger = get_logger("sql_generator")


class SQLGenerator:
    """SQL生成器"""
    
    def __init__(self):
        self.max_retries = 3
        self.common_patterns = {
            "时间范围": [
                r"最近(\d+)天", r"近(\d+)日", r"(\d+)天内", r"过去(\d+)天",
                "最近一周", "最近一月", "最近一年", "今年", "去年"
            ],
            "排序": [
                "最高", "最低", "最大", "最小", "排名", r"前(\d+)", r"后(\d+)",
                "涨幅最大", "跌幅最大", "成交量最大"
            ],
            "比较": [
                "大于", "小于", "超过", "低于", "高于", "等于",
                ">", "<", ">=", "<=", "="
            ],
            "聚合": [
                "平均", "总计", "求和", "最大值", "最小值", "数量", "统计"
            ]
        }
    
    def generate_sql(
        self,
        natural_query: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """生成SQL查询"""
        try:
            logger.info(f"开始生成SQL，查询: '{natural_query}'")
            
            # 获取数据库schema信息
            schema_info = schema_parser.get_schema_summary()
            
            # 分析查询意图
            query_intent = self._analyze_query_intent(natural_query)
            
            # 生成SQL
            sql_result = self._generate_sql_with_llm(
                natural_query, 
                schema_info, 
                query_intent,
                context
            )
            
            if not sql_result.get("success"):
                return sql_result
            
            sql_query = sql_result["sql"]
            
            # 验证SQL
            validation_result = query_validator.validate_sql(sql_query)
            
            if not validation_result["is_valid"]:
                # 尝试修复SQL
                fixed_sql = self._fix_sql_errors(sql_query, validation_result["errors"])
                if fixed_sql:
                    # 重新验证
                    validation_result = query_validator.validate_sql(fixed_sql)
                    if validation_result["is_valid"]:
                        sql_query = fixed_sql
                    else:
                        return {
                            "success": False,
                            "error": "SQL验证失败",
                            "validation_errors": validation_result["errors"],
                            "original_sql": sql_query
                        }
                else:
                    return {
                        "success": False,
                        "error": "SQL生成失败，无法修复错误",
                        "validation_errors": validation_result["errors"],
                        "original_sql": sql_query
                    }
            
            # 构建成功结果
            result = {
                "success": True,
                "sql": sql_query,
                "natural_query": natural_query,
                "query_intent": query_intent,
                "estimated_complexity": self._estimate_query_complexity(sql_query),
                "generated_at": datetime.now().isoformat()
            }
            
            logger.info(f"SQL生成成功: {sql_query[:100]}...")
            return result
            
        except Exception as e:
            logger.error(f"SQL生成失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "natural_query": natural_query
            }
    
    def _analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """分析查询意图"""
        intent = {
            "query_type": "select",  # select, insert, update, delete
            "target_tables": [],
            "time_range": None,
            "sort_order": None,
            "limit": None,
            "aggregation": None,
            "filters": [],
            "keywords": []
        }
        
        query_lower = query.lower()
        
        # 改进的目标表识别 - 更精确的关键词匹配
        table_keywords = {
            "基本信息": ["stock_basic"],
            "基础信息": ["stock_basic"],
            "股票信息": ["stock_basic"],
            "公司信息": ["stock_basic"],
            "名称": ["stock_basic"],
            "行业": ["stock_basic"],
            "地区": ["stock_basic"],
            "价格": ["stock_daily_history"],
            "股价": ["stock_daily_history"],
            "涨跌": ["stock_daily_history"],
            "涨幅": ["stock_daily_history"],
            "跌幅": ["stock_daily_history"],
            "成交量": ["stock_daily_history"],
            "成交额": ["stock_daily_history"],
            "技术指标": ["stock_factor"],
            "MACD": ["stock_factor"],
            "KDJ": ["stock_factor"],
            "RSI": ["stock_factor"],
            "市盈率": ["stock_daily_basic"],
            "市净率": ["stock_daily_basic"],
            "市值": ["stock_daily_basic"],
            "估值": ["stock_daily_basic"],
            "资金流": ["stock_moneyflow"],
            "资金流向": ["stock_moneyflow"]
        }

        # 提取所有关键词
        for word in query.split():
            if len(word) > 1:
                intent["keywords"].append(word)

        # 根据关键词匹配目标表
        for keyword, tables in table_keywords.items():
            if keyword in query:
                intent["target_tables"].extend(tables)

        # 如果没有明确匹配，根据常见查询模式推断
        if not intent["target_tables"]:
            if any(word in query for word in ["平安", "银行", "茅台", "腾讯"]):
                # 查询具体股票，默认查基本信息
                intent["target_tables"].append("stock_basic")

        # 去重
        intent["target_tables"] = list(set(intent["target_tables"]))
        
        # 识别时间范围
        time_patterns = [
            (r"最近(\d+)天", lambda m: int(m.group(1))),
            (r"近(\d+)日", lambda m: int(m.group(1))),
            (r"(\d+)天内", lambda m: int(m.group(1))),
            (r"最近一周", lambda m: 7),
            (r"最近一月", lambda m: 30),
            (r"最近一年", lambda m: 365)
        ]
        
        for pattern, extractor in time_patterns:
            match = re.search(pattern, query)
            if match:
                days = extractor(match)
                intent["time_range"] = {
                    "type": "recent_days",
                    "days": days,
                    "start_date": (date.today() - timedelta(days=days)).isoformat(),
                    "end_date": date.today().isoformat()
                }
                break
        
        # 识别排序
        sort_patterns = [
            (r"前(\d+)", "ASC"),
            (r"后(\d+)", "DESC"),
            (r"最高", "DESC"),
            (r"最低", "ASC"),
            (r"涨幅最大", "DESC"),
            (r"跌幅最大", "ASC")
        ]
        
        for pattern, order in sort_patterns:
            if re.search(pattern, query):
                intent["sort_order"] = order
                # 提取数量限制
                limit_match = re.search(r"前(\d+)|后(\d+)", query)
                if limit_match:
                    intent["limit"] = int(limit_match.group(1) or limit_match.group(2))
                break
        
        # 识别聚合操作
        agg_keywords = {
            "平均": "AVG",
            "总计": "SUM", 
            "求和": "SUM",
            "最大": "MAX",
            "最小": "MIN",
            "数量": "COUNT",
            "统计": "COUNT"
        }
        
        for keyword, agg_func in agg_keywords.items():
            if keyword in query:
                intent["aggregation"] = agg_func
                break
        
        return intent
    
    def _generate_sql_with_llm(
        self,
        natural_query: str,
        schema_info: str,
        query_intent: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """使用LLM生成SQL"""
        try:
            # 构建提示词
            system_prompt = self._build_sql_generation_prompt(schema_info, query_intent)
            
            # 构建用户查询
            user_prompt = f"""
请将以下自然语言查询转换为SQL语句：

查询：{natural_query}

查询意图分析：
- 目标表：{', '.join(query_intent.get('target_tables', []))}
- 时间范围：{query_intent.get('time_range', '无')}
- 排序：{query_intent.get('sort_order', '无')}
- 限制：{query_intent.get('limit', '无')}
- 聚合：{query_intent.get('aggregation', '无')}

请只返回SQL语句，不要包含其他解释。
"""
            
            # 调用LLM
            sql_response = model_manager.generate_sql(
                natural_language_query=user_prompt,
                schema_info=schema_info
            )
            
            # 清理SQL响应
            cleaned_sql = self._clean_sql_response(sql_response)
            
            return {
                "success": True,
                "sql": cleaned_sql,
                "raw_response": sql_response
            }
            
        except Exception as e:
            logger.error(f"LLM SQL生成失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _build_sql_generation_prompt(
        self,
        schema_info: str,
        query_intent: Dict[str, Any]
    ) -> str:
        """构建SQL生成提示词"""

        examples = self._get_sql_examples()

        # 根据查询意图提供更精确的表选择指导
        table_guidance = self._get_table_selection_guidance(query_intent)

        prompt = f"""
你是一个专业的SQL查询生成专家。请根据用户的自然语言描述生成准确的MySQL查询语句。

数据库Schema信息：
{schema_info}

表选择指导：
{table_guidance}

重要规则：
1. 只生成SELECT查询，不允许INSERT、UPDATE、DELETE操作
2. 使用标准的MySQL语法
3. 所有日期字段使用YYYY-MM-DD格式
4. 股票代码字段名为ts_code，格式如'000001.SZ'
5. 价格相关字段通常为DECIMAL类型
6. 时间范围查询使用trade_date字段
7. 如果需要多表关联，使用ts_code字段进行JOIN
8. 限制查询结果数量，避免返回过多数据（默认LIMIT 100）

表选择原则：
- 查询股票基本信息（名称、行业、地区等）→ 使用 stock_basic 表
- 查询价格数据（开高低收、涨跌幅等）→ 使用 stock_daily_history 表
- 查询技术指标（MACD、KDJ、RSI等）→ 使用 stock_factor 表
- 查询基本面数据（市盈率、市净率、市值等）→ 使用 stock_daily_basic 表
- 查询资金流向数据 → 使用 stock_moneyflow 表

常用查询模式：
{examples}

请确保生成的SQL语句：
- 语法正确
- 选择正确的表
- 性能良好
- 安全可靠
- 结果有意义
"""

        return prompt

    def _get_table_selection_guidance(self, query_intent: Dict[str, Any]) -> str:
        """根据查询意图提供表选择指导"""
        keywords = query_intent.get('keywords', [])
        target_tables = query_intent.get('target_tables', [])

        guidance_parts = []

        # 基于关键词的表选择指导
        keyword_table_map = {
            '基本信息': 'stock_basic',
            '基础信息': 'stock_basic',
            '股票信息': 'stock_basic',
            '公司信息': 'stock_basic',
            '名称': 'stock_basic',
            '行业': 'stock_basic',
            '地区': 'stock_basic',
            '价格': 'stock_daily_history',
            '股价': 'stock_daily_history',
            '涨跌': 'stock_daily_history',
            '涨幅': 'stock_daily_history',
            '跌幅': 'stock_daily_history',
            '成交量': 'stock_daily_history',
            '成交额': 'stock_daily_history',
            '技术指标': 'stock_factor',
            'MACD': 'stock_factor',
            'KDJ': 'stock_factor',
            'RSI': 'stock_factor',
            '市盈率': 'stock_daily_basic',
            '市净率': 'stock_daily_basic',
            '市值': 'stock_daily_basic',
            '估值': 'stock_daily_basic',
            '资金流': 'stock_moneyflow',
            '资金流向': 'stock_moneyflow'
        }

        recommended_tables = set()
        for keyword in keywords:
            for key, table in keyword_table_map.items():
                if key in keyword:
                    recommended_tables.add(table)

        if recommended_tables:
            guidance_parts.append(f"基于查询关键词，推荐使用表：{', '.join(recommended_tables)}")

        # 如果有目标表，也加入指导
        if target_tables:
            guidance_parts.append(f"查询意图分析建议的表：{', '.join(target_tables)}")

        return '\n'.join(guidance_parts) if guidance_parts else "请根据查询内容选择合适的表"

    def _get_sql_examples(self) -> str:
        """获取SQL示例"""
        examples = """
1. 查询股票基本信息（使用stock_basic表）：
   SELECT ts_code, name, industry, area FROM stock_basic WHERE name LIKE '%平安%';

2. 查询最近价格数据（使用stock_daily_history表）：
   SELECT ts_code, trade_date, close, pct_chg FROM stock_daily_history
   WHERE trade_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
   ORDER BY trade_date DESC LIMIT 100;

3. 查询技术指标（使用stock_factor表）：
   SELECT ts_code, trade_date, macd_dif, kdj_k, rsi_6 FROM stock_factor
   WHERE ts_code = '000001.SZ' AND trade_date >= '2024-01-01'
   ORDER BY trade_date DESC;

4. 查询涨幅排行（使用stock_daily_history表）：
   SELECT ts_code, trade_date, close, pct_chg FROM stock_daily_history
   WHERE trade_date = (SELECT MAX(trade_date) FROM stock_daily_history)
   ORDER BY pct_chg DESC LIMIT 10;

5. 查询基本面数据（使用stock_daily_basic表）：
   SELECT ts_code, trade_date, pe, pb, total_mv FROM stock_daily_basic
   WHERE trade_date = (SELECT MAX(trade_date) FROM stock_daily_basic)
   AND pe > 0 ORDER BY pe ASC LIMIT 10;

重要提示：
- 查询股票基本信息用stock_basic表
- 查询价格数据用stock_daily_history表
- 查询技术指标用stock_factor表
- 查询基本面数据用stock_daily_basic表
- 查询资金流向用stock_moneyflow表
"""
        return examples
    
    def _clean_sql_response(self, sql_response: str) -> str:
        """清理SQL响应"""
        # 移除markdown代码块标记
        sql_response = re.sub(r'```sql\s*', '', sql_response)
        sql_response = re.sub(r'```\s*', '', sql_response)
        
        # 移除多余的空白字符
        sql_response = sql_response.strip()
        
        # 确保以分号结尾
        if not sql_response.endswith(';'):
            sql_response += ';'
        
        return sql_response
    
    def _fix_sql_errors(
        self, 
        sql: str, 
        errors: List[str]
    ) -> Optional[str]:
        """尝试修复SQL错误"""
        try:
            fixed_sql = sql
            
            # 常见错误修复
            for error in errors:
                error_lower = error.lower()
                
                # 修复表名错误
                if "table" in error_lower and "doesn't exist" in error_lower:
                    # 提取错误的表名并尝试修正
                    table_match = re.search(r"Table '.*?\.(\w+)' doesn't exist", error)
                    if table_match:
                        wrong_table = table_match.group(1)
                        correct_table = self._suggest_correct_table(wrong_table)
                        if correct_table:
                            fixed_sql = fixed_sql.replace(wrong_table, correct_table)
                
                # 修复字段名错误
                if "unknown column" in error_lower:
                    column_match = re.search(r"Unknown column '(\w+)'", error)
                    if column_match:
                        wrong_column = column_match.group(1)
                        correct_column = self._suggest_correct_column(wrong_column)
                        if correct_column:
                            fixed_sql = fixed_sql.replace(wrong_column, correct_column)
            
            return fixed_sql if fixed_sql != sql else None
            
        except Exception as e:
            logger.error(f"修复SQL错误失败: {e}")
            return None
    
    def _suggest_correct_table(self, wrong_table: str) -> Optional[str]:
        """建议正确的表名"""
        table_mapping = {
            "stock": "stock_basic",
            "stocks": "stock_basic", 
            "basic": "stock_basic",
            "daily": "stock_daily_history",
            "history": "stock_daily_history",
            "price": "stock_daily_history",
            "factor": "stock_factor",
            "technical": "stock_factor",
            "fundamental": "stock_daily_basic",
            "money": "stock_moneyflow",
            "flow": "stock_moneyflow"
        }
        
        return table_mapping.get(wrong_table.lower())
    
    def _suggest_correct_column(self, wrong_column: str) -> Optional[str]:
        """建议正确的字段名"""
        column_mapping = {
            "code": "ts_code",
            "stock_code": "ts_code",
            "symbol": "ts_code",
            "stock_name": "name",
            "price": "close",
            "closing_price": "close",
            "date": "trade_date",
            "trading_date": "trade_date",
            "volume": "vol",
            "amount": "amount",
            "change": "pct_chg",
            "change_pct": "pct_chg"
        }
        
        return column_mapping.get(wrong_column.lower())
    
    def _estimate_query_complexity(self, sql: str) -> str:
        """估算查询复杂度"""
        sql_lower = sql.lower()
        
        complexity_score = 0
        
        # 基础查询
        if "select" in sql_lower:
            complexity_score += 1
        
        # JOIN操作
        join_count = sql_lower.count("join")
        complexity_score += join_count * 2
        
        # 子查询
        subquery_count = sql_lower.count("select") - 1
        complexity_score += subquery_count * 3
        
        # 聚合函数
        agg_functions = ["count", "sum", "avg", "max", "min", "group by"]
        for func in agg_functions:
            if func in sql_lower:
                complexity_score += 1
        
        # 排序
        if "order by" in sql_lower:
            complexity_score += 1
        
        # 复杂度分级
        if complexity_score <= 2:
            return "简单"
        elif complexity_score <= 5:
            return "中等"
        else:
            return "复杂"


# 全局SQL生成器实例
sql_generator = SQLGenerator()
