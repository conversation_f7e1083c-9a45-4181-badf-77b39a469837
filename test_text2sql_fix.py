#!/usr/bin/env python3
"""
Text2SQL修复验证测试
"""
import requests
import json
from typing import Dict, Any

def test_text2sql_api(query: str, expected_table: str = None) -> Dict[str, Any]:
    """测试Text2SQL API"""
    api_url = "http://localhost:8000/api/v1/text2sql/query"
    
    payload = {
        "natural_query": query,
        "format_type": "table",
        "execute": True,
        "include_suggestions": True
    }
    
    try:
        response = requests.post(api_url, json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                data = result["data"]
                generated_sql = data.get("generated_sql", "")
                row_count = data.get("row_count", 0)
                execution_time = data.get("execution_time", 0)
                
                print(f"✅ 查询成功: {query}")
                print(f"   生成的SQL: {generated_sql}")
                print(f"   返回行数: {row_count}")
                print(f"   执行时间: {execution_time}s")
                
                if expected_table and expected_table in generated_sql:
                    print(f"   ✅ 正确使用了表: {expected_table}")
                elif expected_table:
                    print(f"   ❌ 未使用预期的表: {expected_table}")
                
                return {"success": True, "data": data}
            else:
                print(f"❌ 查询失败: {result.get('message', '未知错误')}")
                return {"success": False, "error": result.get('message')}
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return {"success": False, "error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return {"success": False, "error": str(e)}

def main():
    """主测试函数"""
    print("🧪 Text2SQL修复验证测试")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        {
            "query": "查询平安银行的基本信息",
            "expected_table": "stock_basic",
            "description": "查询特定股票基本信息"
        },
        {
            "query": "查询银行行业的股票",
            "expected_table": "stock_basic",
            "description": "按行业查询股票"
        },
        {
            "query": "查询贵州茅台的基本信息",
            "expected_table": "stock_basic",
            "description": "查询另一只股票基本信息"
        },
        {
            "query": "显示所有银行股票的名称和地区",
            "expected_table": "stock_basic",
            "description": "查询特定字段"
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['description']}")
        print(f"   查询: {test_case['query']}")
        
        result = test_text2sql_api(
            test_case["query"], 
            test_case.get("expected_table")
        )
        
        if result["success"]:
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有测试通过！Text2SQL功能修复成功！")
    else:
        print("⚠️  部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
