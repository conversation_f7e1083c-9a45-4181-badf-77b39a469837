version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: stock-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: stock_cursor
      MYSQL_USER: stock_user
      MYSQL_PASSWORD: stock_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./data/sql:/docker-entrypoint-initdb.d
    networks:
      - stock-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: stock-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - stock-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Ollama服务
  ollama:
    image: ollama/ollama:latest
    container_name: stock-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - stock-network
    restart: unless-stopped
    environment:
      - OLLAMA_HOST=0.0.0.0

  # 主应用服务
  app:
    build: .
    container_name: stock-app
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=stock_user
      - MYSQL_PASSWORD=stock_password
      - MYSQL_DATABASE=stock_cursor
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - OLLAMA_BASE_URL=http://ollama:11434
      - CHROMA_PERSIST_DIRECTORY=/app/data/chroma
    volumes:
      - ./data/chroma:/app/data/chroma
      - ./logs:/app/logs
    networks:
      - stock-network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Streamlit前端
  frontend:
    build: .
    container_name: stock-frontend
    ports:
      - "8501:8501"
    environment:
      - ENVIRONMENT=production
      - API_PORT=8000
      - API_HOST=app
    command: ["python", "start_streamlit.py"]
    networks:
      - stock-network
    depends_on:
      - app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: stock-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deploy/nginx.conf:/etc/nginx/nginx.conf
      - ./deploy/ssl:/etc/nginx/ssl
    networks:
      - stock-network
    depends_on:
      - app
      - frontend
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  ollama_data:

networks:
  stock-network:
    driver: bridge
