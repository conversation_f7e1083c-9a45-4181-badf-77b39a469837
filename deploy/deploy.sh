#!/bin/bash

# 股票分析RAG系统部署脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p data/chroma
    mkdir -p data/sql
    mkdir -p logs
    mkdir -p deploy/ssl
    
    log_success "目录创建完成"
}

# 生成环境配置
generate_env_config() {
    log_info "生成环境配置..."
    
    if [ ! -f .env.production ]; then
        cat > .env.production << EOF
# 生产环境配置
ENVIRONMENT=production
DEBUG=false

# API配置
API_HOST=0.0.0.0
API_PORT=8000

# 数据库配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=stock_user
MYSQL_PASSWORD=stock_password
MYSQL_DATABASE=stock_cursor

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379

# Ollama配置
OLLAMA_BASE_URL=http://ollama:11434

# ChromaDB配置
CHROMA_PERSIST_DIRECTORY=/app/data/chroma

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log
EOF
        log_success "环境配置文件已生成: .env.production"
    else
        log_info "环境配置文件已存在，跳过生成"
    fi
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    docker-compose build --no-cache
    
    log_success "Docker镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动基础服务
    log_info "启动基础服务 (MySQL, Redis, Ollama)..."
    docker-compose up -d mysql redis ollama
    
    # 等待基础服务就绪
    log_info "等待基础服务就绪..."
    sleep 30
    
    # 检查MySQL连接
    log_info "检查MySQL连接..."
    for i in {1..30}; do
        if docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
            log_success "MySQL连接成功"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "MySQL连接超时"
            exit 1
        fi
        sleep 2
    done
    
    # 启动应用服务
    log_info "启动应用服务..."
    docker-compose up -d app frontend nginx
    
    log_success "所有服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 60
    
    # 检查API服务
    log_info "检查API服务..."
    for i in {1..30}; do
        if curl -f http://localhost/health &> /dev/null; then
            log_success "API服务健康检查通过"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "API服务健康检查失败"
            return 1
        fi
        sleep 2
    done
    
    # 检查前端服务
    log_info "检查前端服务..."
    for i in {1..30}; do
        if curl -f http://localhost:8501 &> /dev/null; then
            log_success "前端服务健康检查通过"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "前端服务健康检查失败"
            return 1
        fi
        sleep 2
    done
    
    log_success "所有服务健康检查通过"
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "🌐 访问地址:"
    echo "  - 前端界面: http://localhost"
    echo "  - API文档: http://localhost/api/docs"
    echo "  - 健康检查: http://localhost/health"
    echo "  - 直接前端: http://localhost:8501"
    echo "  - 直接API: http://localhost:8000"
    echo
    echo "📊 服务状态:"
    docker-compose ps
    echo
    echo "📝 查看日志:"
    echo "  docker-compose logs -f [service_name]"
    echo
    echo "🛑 停止服务:"
    echo "  docker-compose down"
    echo
    echo "🔄 重启服务:"
    echo "  docker-compose restart [service_name]"
}

# 清理函数
cleanup() {
    log_info "清理资源..."
    docker-compose down
    docker system prune -f
    log_success "清理完成"
}

# 主函数
main() {
    echo "🚀 股票分析RAG系统部署脚本"
    echo "================================"
    
    # 解析命令行参数
    case "${1:-deploy}" in
        "deploy")
            check_dependencies
            create_directories
            generate_env_config
            build_images
            start_services
            health_check
            show_deployment_info
            ;;
        "start")
            log_info "启动服务..."
            docker-compose up -d
            health_check
            show_deployment_info
            ;;
        "stop")
            log_info "停止服务..."
            docker-compose down
            log_success "服务已停止"
            ;;
        "restart")
            log_info "重启服务..."
            docker-compose restart
            health_check
            log_success "服务已重启"
            ;;
        "logs")
            docker-compose logs -f
            ;;
        "status")
            docker-compose ps
            ;;
        "cleanup")
            cleanup
            ;;
        "help")
            echo "用法: $0 [command]"
            echo
            echo "命令:"
            echo "  deploy   - 完整部署 (默认)"
            echo "  start    - 启动服务"
            echo "  stop     - 停止服务"
            echo "  restart  - 重启服务"
            echo "  logs     - 查看日志"
            echo "  status   - 查看状态"
            echo "  cleanup  - 清理资源"
            echo "  help     - 显示帮助"
            ;;
        *)
            log_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap cleanup INT TERM

# 运行主函数
main "$@"
