#!/usr/bin/env python3
"""
简化测试运行脚本
"""
import subprocess
import sys
import requests
from pathlib import Path

def check_api_service():
    """检查API服务是否运行"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def run_api_tests():
    """运行API测试"""
    print("🧪 运行API测试...")
    
    if not check_api_service():
        print("❌ API服务未运行，跳过API测试")
        return False
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pytest", "tests/test_api.py", "-v", "--tb=short"],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print("✅ API测试通过")
            return True
        else:
            print("❌ API测试失败")
            print(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ API测试异常: {e}")
        return False

def run_integration_tests():
    """运行集成测试"""
    print("🔗 运行集成测试...")
    
    if not check_api_service():
        print("❌ API服务未运行，跳过集成测试")
        return False
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pytest", "tests/test_integration.py", "-v", "-s", "--tb=short"],
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("✅ 集成测试通过")
            return True
        else:
            print("❌ 集成测试失败")
            print(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试异常: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("🔍 测试基本功能...")
    
    tests = [
        ("健康检查", lambda: requests.get("http://localhost:8000/health", timeout=5).status_code == 200),
        ("系统状态", lambda: requests.get("http://localhost:8000/api/v1/system/status", timeout=10).status_code == 200),
        ("Streamlit前端", lambda: requests.get("http://localhost:8501", timeout=10).status_code == 200),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    return passed == len(tests)

def main():
    """主函数"""
    print("🚀 股票分析RAG系统 - 简化测试")
    print("=" * 50)
    
    # 基本功能测试
    basic_ok = test_basic_functionality()
    
    # API测试
    api_ok = run_api_tests()
    
    # 集成测试
    integration_ok = run_integration_tests()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    
    results = [
        ("基本功能", basic_ok),
        ("API测试", api_ok),
        ("集成测试", integration_ok)
    ]
    
    passed = sum(1 for _, ok in results if ok)
    total = len(results)
    
    for test_name, ok in results:
        status = "✅ 通过" if ok else "❌ 失败"
        print(f"  - {test_name}: {status}")
    
    print(f"\n📋 总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        return True
    else:
        print("⚠️  部分测试失败，但核心功能正常")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
